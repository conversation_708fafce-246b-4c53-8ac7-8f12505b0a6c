@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pendapatan')
@section('content')
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Data Pendapatan</h4>
                    <button class="btn btn-primary">
                        <i class="bx bx-download me-1"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-3">
        <div class="col-lg-3 col-md-6 col-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-success">
                                <i class="bx bx-money fs-4"></i>
                            </span>
                        </div>
                        <div>
                            <small class="text-muted d-block">Total Pendapatan</small>
                            <div class="h5 mb-0">Rp {{ number_format($totalRevenue ?? 0, 0, ',', '.') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-primary">
                                <i class="bx bx-calendar fs-4"></i>
                            </span>
                        </div>
                        <div>
                            <small class="text-muted d-block">Bulan Ini</small>
                            <div class="h5 mb-0">Rp {{ number_format($monthlyRevenue ?? 0, 0, ',', '.') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-warning">
                                <i class="bx bx-time fs-4"></i>
                            </span>
                        </div>
                        <div>
                            <small class="text-muted d-block">Pending</small>
                            <div class="h5 mb-0">Rp {{ number_format($pendingRevenue ?? 0, 0, ',', '.') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-info">
                                <i class="bx bx-receipt fs-4"></i>
                            </span>
                        </div>
                        <div>
                            <small class="text-muted d-block">Total Invoice</small>
                            <div class="h5 mb-0">{{ $totalInvoices ?? 0 }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('pendapatan') }}">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label for="search" class="form-label">Cari</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bx bx-search"></i></span>
                                    <input type="text" id="search" name="search" value="{{ $search ?? '' }}"
                                        placeholder="Cari nama pelanggan atau paket..." class="form-control">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select id="status" name="status" class="form-select">
                                    <option value="">Semua Status</option>
                                    @if (isset($statusOptions))
                                        @foreach ($statusOptions as $statusOption)
                                            <option value="{{ $statusOption->id }}"
                                                {{ ($status ?? '') == $statusOption->id ? 'selected' : '' }}>
                                                {{ $statusOption->nama_status }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="start_date" class="form-label">Dari Tanggal</label>
                                <input type="date" id="start_date" name="start_date" value="{{ $startDate ?? '' }}"
                                    class="form-control">
                            </div>
                            <div class="col-md-2">
                                <label for="end_date" class="form-label">Sampai Tanggal</label>
                                <input type="date" id="end_date" name="end_date" value="{{ $endDate ?? '' }}"
                                    class="form-control">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bx bx-search me-1"></i>Filter
                                    </button>
                                    <a href="{{ route('pendapatan') }}" class="btn btn-outline-secondary">
                                        <i class="bx bx-refresh me-1"></i>Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Data Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Daftar Pendapatan</h5>
                        <small class="text-muted">
                            Menampilkan {{ $invoices->firstItem() ?? 0 }} - {{ $invoices->lastItem() ?? 0 }} dari
                            {{ $invoices->total() ?? 0 }} data
                        </small>
                    </div>
                    <div class="card-body">
                        @if ($invoices && $invoices->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Tanggal</th>
                                            <th>Pelanggan</th>
                                            <th>Paket</th>
                                            <th>Jumlah</th>
                                            <th>Jatuh Tempo</th>
                                            <th>Status</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($invoices as $index => $invoice)
                                            <tr>
                                                <td>{{ $invoices->firstItem() + $index }}</td>
                                                <td>{{ \Carbon\Carbon::parse($invoice->created_at)->format('d/m/Y') }}</td>
                                                <td>
                                                    <div class="fw-medium">
                                                        {{ $invoice->customer->nama_customer ?? 'N/A' }}</div>
                                                    <small
                                                        class="text-muted">{{ $invoice->customer->alamat ?? '' }}</small>
                                                </td>
                                                <td>{{ $invoice->paket->nama_paket ?? 'N/A' }}</td>
                                                <td class="fw-medium">Rp
                                                    {{ number_format($invoice->tagihan, 0, ',', '.') }}</td>
                                                <td>{{ \Carbon\Carbon::parse($invoice->jatuh_tempo)->format('d/m/Y') }}
                                                </td>
                                                <td>
                                                    @if ($invoice->status)
                                                        @if ($invoice->status->nama_status == 'Sudah Bayar')
                                                            <span class="badge bg-label-success">
                                                                <i class="bx bx-check-circle me-1"></i>Sudah Bayar
                                                            </span>
                                                        @elseif($invoice->status->nama_status == 'Belum Bayar')
                                                            <span class="badge bg-label-danger">
                                                                <i class="bx bx-x-circle me-1"></i>Belum Bayar
                                                            </span>
                                                        @else
                                                            <span class="badge bg-label-secondary">
                                                                {{ $invoice->status->nama_status }}
                                                            </span>
                                                        @endif
                                                    @else
                                                        <span class="badge bg-label-secondary">N/A</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow"
                                                            data-bs-toggle="dropdown">
                                                            <i class="bx bx-dots-vertical-rounded"></i>
                                                        </button>
                                                        <div class="dropdown-menu">
                                                            <a class="dropdown-item" href="javascript:void(0);"
                                                                onclick="viewInvoiceDetail({{ $invoice->id }})">
                                                                <i class="bx bx-show me-1"></i> Lihat Detail
                                                            </a>
                                                            @if ($invoice->status && $invoice->status->nama_status == 'Belum Bayar')
                                                                <a class="dropdown-item"
                                                                    href="/payment/invoice/{{ $invoice->id }}">
                                                                    <i class="bx bx-credit-card me-1"></i> Bayar
                                                                </a>
                                                            @endif
                                                            <a class="dropdown-item" href="javascript:void(0);"
                                                                onclick="printInvoice({{ $invoice->id }})">
                                                                <i class="bx bx-printer me-1"></i> Print
                                                            </a>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            @else
                                <!-- Empty State -->
                                <div class="text-center py-5">
                                    <div class="mb-3">
                                        <i class="bx bx-receipt display-4 text-muted"></i>
                                    </div>
                                    <h5 class="mb-2">Tidak ada data pendapatan</h5>
                                    <p class="text-muted">Belum ada data pendapatan yang tersedia untuk ditampilkan.</p>
                                </div>
                        @endif
                    </div>

                    <!-- Pagination -->
                    @if ($invoices && $invoices->hasPages())
                        <div class="card-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    Menampilkan {{ $invoices->firstItem() }} sampai {{ $invoices->lastItem() }} dari
                                    {{ $invoices->total() }} hasil
                                </small>
                                <div>
                                    {{ $invoices->appends(request()->query())->links() }}
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

        </div>

        <!-- Invoice Detail Modal -->
        <div class="modal fade" id="invoiceDetailModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Detail Invoice</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="invoiceDetailContent">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Auto refresh data every 30 seconds
        setInterval(function() {
            refreshRevenueData();
        }, 30000);

        function refreshRevenueData() {
            const form = document.querySelector('form');
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);

            fetch(`{{ route('pendapatan') }}?${params.toString()}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    // Update the page content
                    document.querySelector('.container-xxl').innerHTML = new DOMParser().parseFromString(html,
                        'text/html').querySelector('.container-xxl').innerHTML;
                })
                .catch(error => console.error('Error refreshing data:', error));
        }

        function viewInvoiceDetail(invoiceId) {
            // Show loading state
            document.getElementById('invoiceDetailContent').innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Memuat data...</p>
                </div>
            `;

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('invoiceDetailModal'));
            modal.show();

            // Fetch invoice details (you can implement this endpoint)
            fetch(`/invoice/detail/${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('invoiceDetailContent').innerHTML = data.html;
                })
                .catch(error => {
                    document.getElementById('invoiceDetailContent').innerHTML = `
                        <div class="text-center py-4">
                            <i class="bx bx-error text-danger display-4"></i>
                            <p class="text-muted mt-2">Gagal memuat detail invoice</p>
                        </div>
                    `;
                });
        }

        function printInvoice(invoiceId) {
            window.open(`/invoice/print/${invoiceId}`, '_blank');
        }
    </script>
@endsection

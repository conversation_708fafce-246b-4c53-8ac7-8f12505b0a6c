@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pendapatan')
@section('content')
    <div class="container-fluid px-4 py-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Data Pendapatan</h1>
                    <p class="text-gray-600">Kelola dan pantau data pendapatan perusahaan</p>
                </div>
                <div class="mt-4 sm:mt-0">
                    <button
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center">
                        <i class="bx bx-download mr-2"></i>
                        Export Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Revenue -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="bx bx-money text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Pendapatan</p>
                        <p class="text-2xl font-bold text-gray-900">Rp {{ number_format($totalRevenue ?? 0, 0, ',', '.') }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Monthly Revenue -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100">
                        <i class="bx bx-calendar text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pendapatan Bulan Ini</p>
                        <p class="text-2xl font-bold text-gray-900">Rp
                            {{ number_format($monthlyRevenue ?? 0, 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Pending Revenue -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="bx bx-time text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Pembayaran</p>
                        <p class="text-2xl font-bold text-gray-900">Rp
                            {{ number_format($pendingRevenue ?? 0, 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Total Invoices -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <i class="bx bx-receipt text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Invoice</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $totalInvoices ?? 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <form method="GET" action="{{ route('pendapatan') }}"
                class="space-y-4 lg:space-y-0 lg:flex lg:items-end lg:space-x-4">
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Cari</label>
                    <div class="relative">
                        <input type="text" id="search" name="search" value="{{ $search ?? '' }}"
                            placeholder="Cari nama pelanggan atau paket..."
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="bx bx-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <div class="lg:w-48">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status" name="status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Semua Status</option>
                        <option value="7" {{ ($status ?? '') == '7' ? 'selected' : '' }}>Belum Bayar</option>
                        <option value="8" {{ ($status ?? '') == '8' ? 'selected' : '' }}>Sudah Bayar</option>
                    </select>
                </div>

                <div class="lg:w-40">
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Dari Tanggal</label>
                    <input type="date" id="start_date" name="start_date" value="{{ $startDate ?? '' }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div class="lg:w-40">
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">Sampai Tanggal</label>
                    <input type="date" id="end_date" name="end_date" value="{{ $endDate ?? '' }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div class="flex space-x-2">
                    <button type="submit"
                        class="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center">
                        <i class="bx bx-search mr-2"></i>
                        Filter
                    </button>
                    <a href="{{ route('pendapatan') }}"
                        class="bg-gray-500 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-600 transition-colors duration-200 flex items-center">
                        <i class="bx bx-refresh mr-2"></i>
                        Reset
                    </a>
                </div>
            </form>
        </div>
        <!-- Data Table -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <!-- Table Header -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Daftar Pendapatan</h3>
                    <div class="mt-2 sm:mt-0 text-sm text-gray-500">
                        Menampilkan {{ $invoices->firstItem() ?? 0 }} - {{ $invoices->lastItem() ?? 0 }} dari
                        {{ $invoices->total() ?? 0 }} data
                    </div>
                </div>
            </div>

            <!-- Table Content -->
            <div class="overflow-x-auto" id="revenue-table-container">
                @if ($invoices && $invoices->count() > 0)
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    No
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Tanggal
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Pelanggan
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Paket
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Jumlah
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Jatuh Tempo
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Aksi
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach ($invoices as $index => $invoice)
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $invoices->firstItem() + $index }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ \Carbon\Carbon::parse($invoice->created_at)->format('d/m/Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $invoice->customer->nama_customer ?? 'N/A' }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $invoice->customer->alamat ?? '' }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $invoice->paket->nama_paket ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        Rp {{ number_format($invoice->tagihan, 0, ',', '.') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ \Carbon\Carbon::parse($invoice->jatuh_tempo)->format('d/m/Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if ($invoice->status)
                                            @if ($invoice->status->nama_status == 'Sudah Bayar')
                                                <span
                                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="bx bx-check-circle mr-1"></i>
                                                    Sudah Bayar
                                                </span>
                                            @elseif($invoice->status->nama_status == 'Belum Bayar')
                                                <span
                                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="bx bx-x-circle mr-1"></i>
                                                    Belum Bayar
                                                </span>
                                            @else
                                                <span
                                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    {{ $invoice->status->nama_status }}
                                                </span>
                                            @endif
                                        @else
                                            <span
                                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                N/A
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button
                                                class="text-blue-600 hover:text-blue-900 transition-colors duration-150"
                                                onclick="viewInvoiceDetail({{ $invoice->id }})" title="Lihat Detail">
                                                <i class="bx bx-show text-lg"></i>
                                            </button>
                                            @if ($invoice->status && $invoice->status->nama_status == 'Belum Bayar')
                                                <a href="/payment/invoice/{{ $invoice->id }}"
                                                    class="text-green-600 hover:text-green-900 transition-colors duration-150"
                                                    title="Bayar">
                                                    <i class="bx bx-credit-card text-lg"></i>
                                                </a>
                                            @endif
                                            <button
                                                class="text-gray-600 hover:text-gray-900 transition-colors duration-150"
                                                onclick="printInvoice({{ $invoice->id }})" title="Print">
                                                <i class="bx bx-printer text-lg"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-12">
                        <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
                            <i class="bx bx-receipt text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada data pendapatan</h3>
                        <p class="text-gray-500 mb-6">Belum ada data pendapatan yang tersedia untuk ditampilkan.</p>
                    </div>
                @endif
            </div>

            <!-- Pagination -->
            @if ($invoices && $invoices->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div class="text-sm text-gray-700 mb-4 sm:mb-0">
                            Menampilkan {{ $invoices->firstItem() }} sampai {{ $invoices->lastItem() }} dari
                            {{ $invoices->total() }} hasil
                        </div>
                        <div class="flex justify-center sm:justify-end">
                            {{ $invoices->appends(request()->query())->links('pagination::tailwind') }}
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Invoice Detail Modal -->
    <div id="invoiceDetailModal"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-lg bg-white">
            <div class="mt-3">
                <!-- Modal Header -->
                <div class="flex items-center justify-between pb-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">Detail Invoice</h3>
                    <button onclick="closeInvoiceModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="bx bx-x text-2xl"></i>
                    </button>
                </div>

                <!-- Modal Body -->
                <div id="invoiceDetailContent" class="py-4">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Auto refresh data every 30 seconds
        setInterval(function() {
            refreshRevenueData();
        }, 30000);

        function refreshRevenueData() {
            const form = document.querySelector('form');
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);

            fetch(`{{ route('pendapatan') }}?${params.toString()}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    // Update the page content
                    document.querySelector('.container-fluid').innerHTML = new DOMParser().parseFromString(html,
                        'text/html').querySelector('.container-fluid').innerHTML;
                })
                .catch(error => console.error('Error refreshing data:', error));
        }

        function viewInvoiceDetail(invoiceId) {
            // Show loading state
            document.getElementById('invoiceDetailContent').innerHTML = `
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">Memuat data...</span>
                </div>
            `;

            // Show modal
            document.getElementById('invoiceDetailModal').classList.remove('hidden');

            // Fetch invoice details (you can implement this endpoint)
            fetch(`/invoice/detail/${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('invoiceDetailContent').innerHTML = data.html;
                })
                .catch(error => {
                    document.getElementById('invoiceDetailContent').innerHTML = `
                        <div class="text-center py-8">
                            <i class="bx bx-error text-red-500 text-4xl mb-2"></i>
                            <p class="text-gray-600">Gagal memuat detail invoice</p>
                        </div>
                    `;
                });
        }

        function closeInvoiceModal() {
            document.getElementById('invoiceDetailModal').classList.add('hidden');
        }

        function printInvoice(invoiceId) {
            window.open(`/invoice/print/${invoiceId}`, '_blank');
        }

        // Close modal when clicking outside
        document.getElementById('invoiceDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeInvoiceModal();
            }
        });
    </script>
@endsection

@if($invoices && $invoices->count() > 0)
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    No
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tanggal
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pelanggan
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Paket
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Jumlah
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Jatuh Tempo
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            @foreach($invoices as $index => $invoice)
                <tr class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ $invoices->firstItem() + $index }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ \Carbon\Carbon::parse($invoice->created_at)->format('d/m/Y') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">
                            {{ $invoice->customer->nama_customer ?? 'N/A' }}
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ $invoice->customer->alamat ?? '' }}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ $invoice->paket->nama_paket ?? 'N/A' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        Rp {{ number_format($invoice->tagihan, 0, ',', '.') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ \Carbon\Carbon::parse($invoice->jatuh_tempo)->format('d/m/Y') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        @if($invoice->status)
                            @if($invoice->status->nama_status == 'Sudah Bayar')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="bx bx-check-circle mr-1"></i>
                                    Sudah Bayar
                                </span>
                            @elseif($invoice->status->nama_status == 'Belum Bayar')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="bx bx-x-circle mr-1"></i>
                                    Belum Bayar
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ $invoice->status->nama_status }}
                                </span>
                            @endif
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                N/A
                            </span>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-900 transition-colors duration-150" 
                                    onclick="viewInvoiceDetail({{ $invoice->id }})"
                                    title="Lihat Detail">
                                <i class="bx bx-show text-lg"></i>
                            </button>
                            @if($invoice->status && $invoice->status->nama_status == 'Belum Bayar')
                                <a href="/payment/invoice/{{ $invoice->id }}" 
                                   class="text-green-600 hover:text-green-900 transition-colors duration-150"
                                   title="Bayar">
                                    <i class="bx bx-credit-card text-lg"></i>
                                </a>
                            @endif
                            <button class="text-gray-600 hover:text-gray-900 transition-colors duration-150" 
                                    onclick="printInvoice({{ $invoice->id }})"
                                    title="Print">
                                <i class="bx bx-printer text-lg"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
@else
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
            <i class="bx bx-receipt text-6xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada data pendapatan</h3>
        <p class="text-gray-500 mb-6">Belum ada data pendapatan yang tersedia untuk ditampilkan.</p>
    </div>
@endif
